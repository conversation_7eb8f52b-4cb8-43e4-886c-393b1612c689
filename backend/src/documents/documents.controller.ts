import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  UseGuards,
  Req,
  ForbiddenException,
  Inject,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { DocumentsService } from "./documents.service";
import { UserRole } from "@shared/types";
import { DocumentType } from "src/common/enums/document-type.enum";
import {
  UploadDocumentSwaggerDto,
  VerifyDocumentSwaggerDto,
  DocumentResponseSwaggerDto,
} from "../users/dto/swagger.dto";

@ApiTags("documents")
@Controller("documents")
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class DocumentsController {
  constructor(
    @Inject(DocumentsService)
    private readonly documentsService: DocumentsService
  ) {}

  @Post("upload")
  @ApiOperation({ summary: "Upload a new document" })
  @ApiBody({
    type: UploadDocumentSwaggerDto,
    description: "Document details to upload",
  })
  @ApiResponse({
    status: 201,
    description: "Document uploaded successfully",
    type: DocumentResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Invalid document data" })
  async uploadDocument(
    @Req() req: any,
    @Body()
    body: {
      documentType: DocumentType;
      documentUrl: string;
      documentNumber?: string;
    }
  ) {
    const { documentType, documentUrl, documentNumber } = body;
    return this.documentsService.uploadDocument(
      req.user.id,
      documentType,
      documentUrl,
      documentNumber
    );
  }

  @Post("verify/:id")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Verify or reject a document (Admin only)" })
  @ApiParam({
    name: "id",
    description: "Document ID to verify",
    type: "string",
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @ApiBody({
    type: VerifyDocumentSwaggerDto,
    description: "Verification details",
  })
  @ApiResponse({
    status: 200,
    description: "Document verification status updated",
    type: DocumentResponseSwaggerDto,
  })
  @ApiResponse({ status: 400, description: "Invalid verification data" })
  @ApiResponse({ status: 404, description: "Document not found" })
  async verifyDocument(
    @Req() req: any,
    @Param("id") documentId: string,
    @Body() body: { isApproved: boolean; rejectionReason?: string }
  ) {
    const { isApproved, rejectionReason } = body;
    return this.documentsService.verifyDocument(
      documentId,
      req.user.id,
      isApproved,
      rejectionReason
    );
  }

  @Get("user/:userId")
  @ApiOperation({ summary: "Get documents for a specific user" })
  @ApiParam({
    name: "userId",
    description: "User ID to get documents for",
    type: "string",
    example: "550e8400-e29b-41d4-a716-446655440000",
  })
  @ApiResponse({
    status: 200,
    description: "User documents retrieved successfully",
    type: [DocumentResponseSwaggerDto],
  })
  @ApiResponse({
    status: 403,
    description: "Forbidden - can only view your own documents",
  })
  @ApiResponse({ status: 404, description: "User not found" })
  async getUserDocuments(@Param("userId") userId: string, @Req() req) {
    // Only allow admins or the user themselves to view documents
    if (req.user.role !== UserRole.ADMIN && req.user.id !== userId) {
      throw new ForbiddenException("You can only view your own documents");
    }
    return this.documentsService.getUserDocuments(userId);
  }

  @Get("pending")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Get all pending documents (Admin only)" })
  @ApiResponse({
    status: 200,
    description: "Pending documents retrieved successfully",
    type: [DocumentResponseSwaggerDto],
  })
  async getPendingDocuments() {
    return this.documentsService.getAllPendingDocuments();
  }

  @Get()
  @ApiOperation({ summary: "Get current user's documents" })
  @ApiResponse({
    status: 200,
    description: "User documents retrieved successfully",
    type: [DocumentResponseSwaggerDto],
  })
  async getMyDocuments(@Req() req) {
    return this.documentsService.getUserDocuments(req.user.id);
  }
}
